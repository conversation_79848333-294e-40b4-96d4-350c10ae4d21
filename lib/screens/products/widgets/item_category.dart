import 'dart:developer';

import 'package:flutter/material.dart';

import '../../../common/config.dart';
import '../../../widgets/common/index.dart' show FluxImage;
import 'category_filter_templets_widgets/clothes_category_product_filter.widget.dart'
    show ClothesCategoryProductFilterWidget;
import 'category_filter_templets_widgets/restaurant_category_product_filter.widget.dart'
    show RestaurantCategoryProductFilterWidget;

class ItemCategory extends StatelessWidget {
  final String? categoryId;
  final String categoryName;
  final String? categoryImage;
  final bool isSelected;
  final Function(String?)? onTap;
  final bool isClothesLayout;

  const ItemCategory({
    super.key,
    this.categoryId,
    required this.categoryName,
    this.categoryImage,
    this.isSelected = false,
    this.isClothesLayout = false,
    this.onTap,
  });

  // TODO-CategoryFilterProducts

  @override
  Widget build(BuildContext context) {
    var highlightColor = isSelected
        ? Theme.of(context).colorScheme.secondary.withOpacity(0.15)
        : Colors.transparent;

    log('asfasfasf ${isSelected}');

    if (currentVendor?.isRestaurant == true ||
        currentVendor?.isMarket == true) {
      return RestaurantCategoryProductFilterWidget(
        categoryId: categoryId,
        categoryName: categoryName,
        categoryImage: categoryImage,
        isSelected: isSelected,
        onTap: onTap,
      );
    }
    if (currentVendor?.isClothes == true || isClothesLayout) {
      return ClothesCategoryProductFilterWidget(
        categoryId: categoryId,
        categoryName: categoryName,
        categoryImage: categoryImage,
        isSelected: isSelected,
        onTap: onTap,
      );
    }

    return GestureDetector(
      onTap: () => onTap?.call(categoryId),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(
          horizontal: categoryImage != null ? 5 : 10,
          vertical: 4,
        ),
        margin: const EdgeInsets.only(left: 5, top: 10, bottom: 4),
        decoration: BoxDecoration(
          color: highlightColor,
          borderRadius: BorderRadius.circular(18),
        ),
        child: categoryImage != null
            ? Container(
                width: 70,
                margin: const EdgeInsets.only(top: 8, left: 4, right: 4),
                child: Column(
                  children: <Widget>[
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: SizedBox(
                        width: 50,
                        height: 50,
                        child: FluxImage(
                          imageUrl: categoryImage!,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8.0),
                    SizedBox(
                      child: Text(
                        categoryName.toUpperCase(),
                        maxLines: 2,
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall!
                            .copyWith(
                              fontWeight: FontWeight.w500,
                            )
                            .apply(
                              fontSizeFactor: 0.7,
                            ),
                        textAlign: TextAlign.center,
                        softWrap: true,
                        overflow: TextOverflow.visible,
                      ),
                    ),
                  ],
                ),
              )
            : Center(
                child: Text(
                  categoryName.toUpperCase(),
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        letterSpacing: 0.5,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                ),
              ),
      ),
    );
  }
}
