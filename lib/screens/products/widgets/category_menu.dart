import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:provider/provider.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import '../../../common/config.dart';
import '../../../common/constants.dart';
import '../../../common/logger.dart';
import '../../../frameworks/strapi/services/strapi_service.dart';
import '../../../generated/l10n.dart' show S;
import '../../../models/index.dart';
import '../../../widgets/common/flux_image.dart' show FluxImage;
import 'item_category.dart';

class ProductCategoryMenu extends StatefulWidget {
  final bool enableSearchHistory;
  final bool imageLayout;
  final List<String>? selectedCategories;
  final Function(String?)? onTap;
  final EdgeInsetsGeometry? padding;
  final bool isClothesLayout;

  const ProductCategoryMenu({
    super.key,
    this.enableSearchHistory = false,
    this.imageLayout = false,
    this.selectedCategories,
    this.onTap,
    this.padding,
    this.isClothesLayout = false,
  });

  @override
  StateProductCategoryMenu createState() => StateProductCategoryMenu();
}

class StateProductCategoryMenu extends State<ProductCategoryMenu> {
  bool get categoryImageMenu => kAdvanceConfig.categoryImageMenu;

  final itemScrollController = ItemScrollController();
  var firstJumpDone = false;
  String? parentOfSelectedCategoryId;
  Category? selectedCategoryModel;

  void _animateToCategory(int index) {
    if (firstJumpDone) return;
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (index > 3) {
        if (itemScrollController.isAttached) {
          itemScrollController.scrollTo(
            index: index - 1,
            duration: const Duration(milliseconds: 250),
          );
          firstJumpDone = true;
        }
      }
    });
  }

  Widget renderListCategories(
    List<Category> categories,
  ) {
    var showImageMenu = categoryImageMenu && widget.imageLayout;

    final isClothes =
        widget.isClothesLayout || currentVendor?.isClothes == true;

    final height = isClothes
        ? 60
        : showImageMenu
            ? 130
            : 50;

    if (isClothes) {
      return Column(
        children: [
          Container(
            padding: widget.padding ??
                const EdgeInsets.symmetric(horizontal: 8).copyWith(bottom: 4),
            color: Theme.of(context).colorScheme.surface,
            constraints: const BoxConstraints(minHeight: 40),
            height: height.toDouble(),
            child: Center(
              child: ScrollablePositionedList.builder(
                scrollDirection: Axis.horizontal,
                itemCount: categories.length,
                itemScrollController: itemScrollController,
                itemBuilder: (context, index) {
                  var category = categories[index];
                  return ItemCategory(
                    categoryId: category.id,
                    categoryName: category.name!,
                    categoryImage: showImageMenu ? category.image : null,
                    isSelected: selectedCategoryModel?.id == category.id,
                    isClothesLayout: isClothes,
                    onTap: (id) {
                      setState(() {
                        selectedCategoryModel = category;
                      });
                      widget.onTap?.call(id);
                    },
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 5),
          if (selectedCategoryModel != null)
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(opacity: animation, child: child);
              },
              child: FluxImage(
                key: ValueKey<String>(selectedCategoryModel?.image ?? ''),
                height: 350,
                width: context.screenWidth,
                imageUrl: selectedCategoryModel?.image ?? '',
                fit: BoxFit.cover,
              ),
            )
        ],
      );
    }


    return Container(
      padding: widget.padding ??
          const EdgeInsets.symmetric(horizontal: 8).copyWith(bottom: 4),
      color: Theme.of(context).colorScheme.surface,
      constraints: const BoxConstraints(minHeight: 40),
      height: height.toDouble(),
      child: Center(
        child: ScrollablePositionedList.builder(
          scrollDirection: Axis.horizontal,
          itemCount: categories.length,
          itemScrollController: itemScrollController,
          itemBuilder: (context, index) {
            var category = categories[index];

            return ItemCategory(
              categoryId: category.id,
              categoryName: category.name!,
              categoryImage: showImageMenu ? category.image : null,
              isSelected: selectedCategoryModel?.id == category.id,
              onTap: (id) {
                // if(subCa)
                // Navigator.push(context, MaterialPageRoute(builder: (context) {
                //   return const CategoriesScreen();
                // }));
                setState(() {
                  selectedCategoryModel = category;
                });
                widget.onTap?.call(id);
              },
            );
          },
        ),
      ),
    );
  }

  var selectedCategoryIds;

  @override
  Widget build(BuildContext context) {
    Log.w('==========================: ${widget.selectedCategories}');

    if (widget.enableSearchHistory) {
      return const SizedBox(width: double.infinity);
    }

    return ValueListenableBuilder(
        valueListenable: selectedSubCat,
        builder: (context, Category? selectedMainCategoryValue, child) {
          return Consumer<CategoryModel>(
              builder: (context, categoryModel, child) {
            if (categoryModel.isLoading) {
              return Center(child: kLoadingWidget(context));
            }
            // final categories = categoryModel.categories ?? <Category>[];

            final categories = selectedMainCategoryValue == null
                ? categoryModel.categories ?? <Category>[]
                : selectedMainCategoryValue.subCategories ?? [];

            if (selectedMainCategoryValue != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                printLog(
                    'ffff ${selectedCategoryModel} assafsafsf ${selectedMainCategoryValue.subCategories.map(
                  (e) => e.name,
                )}');

                if (selectedMainCategoryValue.subCategories.isNotEmpty &&
                    selectedMainCategoryValue.subCategories.firstOrNull !=
                        null &&
                    selectedCategoryModel == null) {
                  setState(() {
                    selectedCategoryModel = selectedMainCategoryValue
                        .subCategories
                        .firstWhereOrNull((element) =>
                            element.id ==
                            selectedMainCategoryValue
                                .subCategories.firstOrNull?.id);
                    selectedCategoryIds = [
                      selectedMainCategoryValue.subCategories.firstOrNull?.id ??
                          ''
                    ];
                  });
                }
              });

              // if (selectedMainCategoryValue.isReservations) {
              //   return renderListCategories(
              //     reservationSubCategories,
              //   );
              // }
              //
              // if (selectedMainCategoryValue.isOrderFood) {
              //   return renderListCategories(
              //     orderFoodSubCategories,
              //   );
              // }
              //
              // if (selectedMainCategoryValue.isOrderMarket) {
              //   return renderListCategories(
              //     orderMarketSubCategories,
              //   );
              // }
              //
              // if (selectedMainCategoryValue.isShoppingOnline) {
              //   return renderListCategories(
              //     shoppingOnlineSubCategories,
              //   );
              // }

              //TODO-MainCategorySubCategories
              if (selectedMainCategoryValue.subCategories.isEmpty) {
                return const SizedBox();
              }

              return renderListCategories(categories);
            }

            selectedCategoryModel ??= categories.firstWhereOrNull(
              (element) => element.id == widget.selectedCategories?.first,
            );

            selectedCategoryIds = widget.selectedCategories;

            if (selectedCategoryIds?.length != 1) {
              return const SizedBox();
            }

            final selectedCategoryId = selectedCategoryIds?.first;

            final selectedCategory =
                categoryModel.categoryList[selectedCategoryId];

            if (selectedCategory == null) {
              return const SizedBox();
            }

            // if selected category has parent, don't need to check selected category is parent
            if (parentOfSelectedCategoryId == null) {
              final subCategoriesOfSelectedId =
                  getSubCategories(categories, selectedCategoryId);

              // if selected category has sub categories, render selected category first
              // then render sub categories
              if (subCategoriesOfSelectedId.isNotEmpty) {
                subCategoriesOfSelectedId.insert(0, selectedCategory);
                final selectedIndex = subCategoriesOfSelectedId
                    .indexWhere((o) => o.id == selectedCategoryId);
                _animateToCategory(selectedIndex);
                return renderListCategories(subCategoriesOfSelectedId);
              }
            }

            // if selected category has no sub categories, render all the categories
            // the same level
            // just find the parentOfSelectedCategory for first init
            if (parentOfSelectedCategoryId == null && firstJumpDone == false) {
              parentOfSelectedCategoryId = getParentCategories(
                  categoryModel.categories, selectedCategoryId);
            }

            // if selected category has no parent, render all categories (Shopify case)
            if (parentOfSelectedCategoryId == null) {
              final selectedIndex =
                  categories.indexWhere((o) => o.id == selectedCategoryId);
              _animateToCategory(selectedIndex);

              return renderListCategories(categories);
            }

            // =============================== //
            final parentCategoryOfSelectedCategory = categoryModel
                .categoryList[parentOfSelectedCategoryId.toString()]!
                .copyWith(name: S.of(context).seeAll);

            final listSubCategory =
                getSubCategories(categories, parentOfSelectedCategoryId);
            if (listSubCategory.length < 2) {
              return const SizedBox(width: double.infinity);
            }

            listSubCategory.insert(0, parentCategoryOfSelectedCategory);
            final selectedIndex =
                listSubCategory.indexWhere((o) => o.id == selectedCategoryId);
            _animateToCategory(selectedIndex);
            return renderListCategories(listSubCategory);
          });
        });
  }

  String? getParentCategories(List<Category>? categories, id) {
    for (var item in (categories ?? <Category>[])) {
      if (item.id == id) {
        return (item.parent == null || item.isRoot) ? null : item.parent;
      }
    }
    return null;
  }

  List<Category> getSubCategories(List<Category> categories, String? id) {
    return categories.where((o) => o.parent == id).toList();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
