import 'package:flutter/material.dart';
import 'package:flutter_zoom_drawer/flutter_zoom_drawer.dart';
import 'package:fstore/common/tools/image_resize.dart';
import 'package:fstore/models/entities/category.dart';
import 'package:fstore/modules/dynamic_layout/config/category_config.dart';

import '../../../../frameworks/strapi/services/strapi_service.dart';
import '../../../../generated/l10n.dart';
import '../../../../models/product_model.dart';
import '../../../../screens/categories/categories_screen.dart';
import '../../../../widgets/common/flux_image.dart';
import '../common_item_extension.dart';

class MainCategoryImageItem extends StatelessWidget {
  final Category cat;
  final products;
  final width;
  final height;
  final CommonItemConfig commonConfig;

  const MainCategoryImageItem({
    required this.cat,
    this.products,
    this.width,
    this.height,
    required this.commonConfig,
  });

  void _navigateToCategories(BuildContext context) {
    selectedMainCategory.value = cat;

    final selectedMainCat = selectedMainCategory.value ?? Category();

    final subCategories = selectedMainCat.isReservations
        ? reservationSubCategories
        : selectedMainCat.isOrderMarket
            ? orderMarketSubCategories
            : selectedMainCat.isOrderFood
                ? orderFoodSubCategories
                : selectedMainCat.isShoppingOnline
                    ? shoppingOnlineSubCategories
                    : selectedMainCat.subCategories;

    selectedSubCategories.value = subCategories;

    Navigator.push(context, MaterialPageRoute(builder: (context) {
      return const CategoriesScreen(
        categoryLayout: 'grid',
      );
    }));
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final itemWidth = width ?? screenSize.width / 3;

    final name = cat.name;
    final image = cat.image;

    final imageWidget = cat.image != null
        ? FluxImage(
            imageUrl: cat.image!,
            height: height,
            width: itemWidth,
            fit: BoxFit.fill,
            // commonConfig.boxFit,
          )
        : null;
    const border = 0;
    // commonConfig.enableBorder ? (commonConfig.border ?? 0.5) : 0;

    return GestureDetector(
      onTap: () => _navigateToCategories(context),
      child: Container(
        decoration: BoxDecoration(
            border: border > 0
                ? Border.all(
                    color: Theme.of(context)
                        .colorScheme
                        .secondary
                        .withOpacity(0.5),
                    width: border.toDouble(),
                  )
                : null,
            borderRadius: border > 0
                ? const BorderRadius.all(Radius.circular(5.0))
                : null,
            boxShadow: [
              if (commonConfig.boxShadow != null)
                BoxShadow(
                  blurRadius: commonConfig.boxShadow!.blurRadius,
                  color: Theme.of(context)
                      .colorScheme
                      .secondary
                      .withOpacity(commonConfig.boxShadow!.colorOpacity),
                  offset: Offset(
                      commonConfig.boxShadow!.x, commonConfig.boxShadow!.y),
                )
            ]),
        width: itemWidth,
        height: double.tryParse(height?.toString() ?? '180.0') ?? 180.0,
        // padding: EdgeInsets.symmetric(
        //   horizontal: commonConfig.paddingX,
        //   vertical: commonConfig.paddingY,
        // ),
        // margin: EdgeInsets.symmetric(
        //   horizontal: commonConfig.marginX,
        //   vertical: commonConfig.marginY,
        // ),
        child: Container(
          width: itemWidth,
          decoration: commonConfig.imageDecoration,
          padding: EdgeInsets.all(commonConfig.imageSpacing),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(15),
            // BorderRadius.circular(commonConfig.radius ?? 0.0),
            child: imageWidget ??
                ImageResize(
                  url: image,
                  fit: commonConfig.boxFit,
                  isResize: true,
                  size: kSize.small,
                ),
          ),
        ),
      ),
    );
  }
}

// New compact main category widget for the new layout
class CompactMainCategoryItem extends StatelessWidget {
  final Category cat;
  final CommonItemConfig commonConfig;

  const CompactMainCategoryItem({
    super.key,
    required this.cat,
    required this.commonConfig,
  });

  void _navigateToCategories(BuildContext context) {
    selectedMainCategory.value = cat;

    final selectedMainCat = selectedMainCategory.value ?? Category();

    final subCategories = selectedMainCat.isReservations
        ? reservationSubCategories
        : selectedMainCat.isOrderMarket
            ? orderMarketSubCategories
            : selectedMainCat.isOrderFood
                ? orderFoodSubCategories
                : selectedMainCat.isShoppingOnline
                    ? shoppingOnlineSubCategories
                    : selectedMainCat.subCategories;

    selectedSubCategories.value = subCategories;

    final isMarket = (cat.name?.contains('Market') ?? false) ||
        (cat.name?.contains('ماركت') ?? false);

    Navigator.push(context, MaterialPageRoute(builder: (context) {
      return CategoriesScreen(
        categoryLayout: isMarket ? 'card' : 'grid',
        isMarket: isMarket,
      );
    }));
  }

  @override
  Widget build(BuildContext context) {
    final isMarket = (cat.name?.contains('Market') ?? false) ||
        (cat.name?.contains('ماركت') ?? false);
    return Row(
      children: [
        // // Circle avatar
        // GestureDetector(
        //   onTap: () => isMarket ? null : _navigateToCategories(context),
        //   child: CircleAvatar(
        //     radius: 20,
        //     backgroundImage:
        //         cat.image != null ? NetworkImage(cat.image!) : null,
        //     backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
        //     child: cat.image == null
        //         ? Icon(
        //             Icons.category,
        //             color: Theme.of(context).primaryColor,
        //           )
        //         : null,
        //   ),
        // ),
        // const SizedBox(width: 12),
        // // Category name
        Expanded(
          child: GestureDetector(
            onTap: () => _navigateToCategories(context),
            child: Text(
              cat.name ?? '',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),
        ),
        // See all button

        GestureDetector(
          onTap: () => _navigateToCategories(context),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              S.of(context).seeAll,
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Subcategory item widget for horizontal list
class SubCategoryHorizontalItem extends StatelessWidget {
  final Category category;
  final CommonItemConfig commonConfig;
  final bool isMarket;

  const SubCategoryHorizontalItem({
    super.key,
    required this.category,
    required this.commonConfig,
    this.isMarket = false,
  });

  void _navigateToProducts(BuildContext context) {
    selectedSubCat.value = category;

    // Use the same logic as in card.dart
    final cateName = category.subCategories.isNotEmpty
        ? category.subCategories.firstOrNull?.name ?? category.name
        : category.name;
    final cateId = category.subCategories.firstOrNull?.id ?? category.id;

    // Navigate to products using ProductModel
    ProductModel.showList(
      cateName: cateName,
      cateId: cateId,
    );
  }

  @override
  Widget build(BuildContext context) {
    const height = 120.0;
    final width = isMarket ? 180.0 : 220.0;

    return GestureDetector(
      onTap: () => _navigateToProducts(context),
      child: Container(
        width: width,
        margin: const EdgeInsets.only(right: 12),
        child: Column(
          children: [
            // Category image
            Container(
              height: height,
              width: width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: Theme.of(context).primaryColor.withOpacity(0.1),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: category.image != null
                    ? FluxImage(
                        imageUrl: category.image!,
                        height: height,
                        width: width,
                        fit: BoxFit.fill,
                      )
                    : Icon(
                        Icons.category,
                        color: Theme.of(context).primaryColor,
                        size: 30,
                      ),
              ),
            ),
            const SizedBox(height: 8),
            // Category name
            Text(
              category.name ?? '',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

//import 'package:flutter/material.dart';
// import 'package:fstore/common/tools/image_resize.dart';
// import 'package:fstore/models/entities/category.dart';
// import 'package:fstore/models/product_model.dart';
// import 'package:fstore/modules/dynamic_layout/config/category_config.dart';
//
// import '../../../../frameworks/strapi/services/strapi_service.dart';
// import '../../../../widgets/common/flux_image.dart';
// import '../common_item_extension.dart';
//
// class MainCategoryImageItem extends StatelessWidget {
//   final Category cat;
//   final products;
//   final width;
//   final height;
//   final CommonItemConfig commonConfig;
//
//   const MainCategoryImageItem({
//     required this.cat,
//     this.products,
//     this.width,
//     this.height,
//     required this.commonConfig,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final screenSize = MediaQuery.of(context).size;
//     final itemWidth = width ?? screenSize.width / 3;
//
//     final name = cat.name;
//     final image = cat.image;
//
//     final imageWidget = cat.image != null
//         ? FluxImage(
//             imageUrl: cat.image!,
//             height: height,
//             width: itemWidth,
//             fit: BoxFit.cover,
//             // commonConfig.boxFit,
//           )
//         : null;
//     const border = 0;
//     // commonConfig.enableBorder ? (commonConfig.border ?? 0.5) : 0;
//
//     return Padding(
//       padding: const EdgeInsets.only(bottom: 10.0, left: 5.0, right: 10),
//       child: GestureDetector(
//         onTap: () {
//           // FluxNavigate.pushNamed(
//           //   RouteList.backdrop,
//           //   arguments: BackDropArguments(
//           //     config: cat.toJson(),
//           //     cateName: name,
//           //   ),
//           // );
//
//           selectedMainCategory.value = cat;
//
//           ProductModel.showList(
//             cateName: cat.subCategories.isNotEmpty
//                 ? cat.subCategories.firstOrNull?.name ?? name
//                 : name,
//             cateId: cat.subCategories.firstOrNull?.id ?? cat.id,
//           );
//         },
//         child: Container(
//           decoration: BoxDecoration(
//               border: border > 0
//                   ? Border.all(
//                       color: Theme.of(context)
//                           .colorScheme
//                           .secondary
//                           .withOpacity(0.5),
//                       width: border.toDouble(),
//                     )
//                   : null,
//               borderRadius: border > 0
//                   ? const BorderRadius.all(Radius.circular(5.0))
//                   : null,
//               boxShadow: [
//                 if (commonConfig.boxShadow != null)
//                   BoxShadow(
//                     blurRadius: commonConfig.boxShadow!.blurRadius,
//                     color: Theme.of(context)
//                         .colorScheme
//                         .secondary
//                         .withOpacity(commonConfig.boxShadow!.colorOpacity),
//                     offset: Offset(
//                         commonConfig.boxShadow!.x, commonConfig.boxShadow!.y),
//                   )
//               ]),
//           width: itemWidth,
//           height: double.tryParse(height?.toString() ?? '180.0') ?? 180.0,
//           padding: EdgeInsets.symmetric(
//             horizontal: commonConfig.paddingX,
//             vertical: commonConfig.paddingY,
//           ),
//           margin: EdgeInsets.symmetric(
//             horizontal: commonConfig.marginX,
//             vertical: commonConfig.marginY,
//           ),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: <Widget>[
//               Expanded(
//                 child: Container(
//                   width: itemWidth,
//                   decoration: commonConfig.imageDecoration,
//                   padding: EdgeInsets.all(commonConfig.imageSpacing),
//                   child: ClipRRect(
//                     borderRadius: BorderRadius.circular(15),
//                     // BorderRadius.circular(commonConfig.radius ?? 0.0),
//                     child: imageWidget ??
//                         ImageResize(
//                           url: image,
//                           fit: commonConfig.boxFit,
//                           isResize: true,
//                           size: kSize.small,
//                         ),
//                   ),
//                 ),
//               ),
//               Align(
//                 alignment: Alignment.center,
//                 // commonConfig.alignment,
//                 child: Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 5),
//                   child: Column(
//                     crossAxisAlignment: [
//                       Alignment.center,
//                       Alignment.topCenter,
//                       Alignment.bottomCenter
//                     ].contains(commonConfig.alignment)
//                         ? CrossAxisAlignment.center
//                         : CrossAxisAlignment.start,
//                     mainAxisSize: MainAxisSize.min,
//                     children: <Widget>[
//                       // if (cat.showText ?? false) ...[
//                       const SizedBox(height: 8),
//                       Text(
//                         cat.name ?? name!,
//                         style:
//                             Theme.of(context).textTheme.titleMedium?.copyWith(
//                                   fontSize: commonConfig.labelFontSize,
//                                 ),
//                       ),
//                       // ],
//                       // if (cat.showDescription) ...[
//                       //   const SizedBox(height: 4),
//                       //   Text(
//                       //     cat.description ??
//                       //         S.of(context).totalProducts('$total'),
//                       //     style: const TextStyle(
//                       //       fontSize: 9,
//                       //     ),
//                       //   ),
//                       // ]
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
