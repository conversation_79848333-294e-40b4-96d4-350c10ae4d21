# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: "16e298750b6d0af7ce8a3ba7c18c69c3785d11b15ec83f6dcd0ad2a0009b3cab"
      url: "https://pub.dev"
    source: hosted
    version: "76.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "7fd72d77a7487c26faab1d274af23fb008763ddc10800261abbfb2c067f183d5"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.53"
  _macros:
    dependency: transitive
    description: dart
    source: sdk
    version: "0.3.3"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "1f14db053a8c23e260789e9b0980fa27f2680dd640932cae5e1137cce0e46e1e"
      url: "https://pub.dev"
    source: hosted
    version: "6.11.0"
  animated_text_kit:
    dependency: "direct main"
    description:
      name: animated_text_kit
      sha256: "37392a5376c9a1a503b02463c38bc0342ef814ddbb8f9977bc90f2a84b22fa92"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  ansicolor:
    dependency: transitive
    description:
      name: ansicolor
      sha256: "50e982d500bc863e1d703448afdbf9e5a72eb48840a4f766fa361ffd6877055f"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  app_tracking_transparency:
    dependency: "direct main"
    description:
      name: app_tracking_transparency
      sha256: ce9311f0e393dbd6b1cb4aeaf609e2db8ba20b1327ca67d07c11ef4876f843a8
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  archive:
    dependency: "direct overridden"
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: d0481093c50b1da8910eb0bb301626d4d8eb7284aa739614d2b394ee09e3ea04
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: "068190d6c99c436287936ba5855af2e1fa78d8083ae65b4db6a281780da727ae"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  async:
    dependency: "direct main"
    description:
      name: async
      sha256: "758e6d74e971c3e5aceb4110bfd6698efc7f501675bcfe0c775459a8140750eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: "106842ad6569f0b60297619e9e0b1885c2fb9bf84812935490e6c5275777804e"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.4"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  build:
    dependency: transitive
    description:
      name: build
      sha256: cef23f1eda9b57566c81e2133d196f8e3df48f244b317368d65c5943d91148f0
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: "4ae2de3e1e67ea270081eaee972e1bd8f027d459f249e0f1186730784c2e7e33"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "8e928697a82be082206edb0b9c99c5a4ad6bc31c9e9b8b2f291ae65cd4a25daa"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: b9e4fda21d846e192628e7a4f6deda6888c36b5b69ba02ff291a01fd529140f0
      url: "https://pub.dev"
    source: hosted
    version: "2.4.4"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "581bacf68f89ec8792f5e5a0b2c4decd1c948e97ce659dc783688c8a88fbec21"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.8"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: f8126682b87a7282a339b871298cc12009cb67109cfa1614d6436fb0289193e0
      url: "https://pub.dev"
    source: hosted
    version: "7.3.2"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: ea90e81dc4a25a043d9bee692d20ed6d1c4a1662a28c03a96417446c093ed6b4
      url: "https://pub.dev"
    source: hosted
    version: "8.9.5"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: "28ea9690a8207179c319965c13cd8df184d5ee721ae2ce60f398ced1219cea1f"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "9e90e78ae72caa874a323d78fa6301b3fb8fa7ea76a8f96dc5b5bf79f283bf2f"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "205d6a9f1862de34b93184f22b9d2d94586b2f05c581d546695e3d8f6a805cd7"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: "7b006ec356205054af5beaef62e2221160ea36b90fb70a35e4deacd49d0349ae"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb0f1107cac15a5ea6ef0a6ef71a807b9e4267c713bb93e00e92d737cc8dbd8a
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      sha256: ff6785f7e9e3c38ac98b2fb035701789de90154024a75b6cb926445e83197d1c
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  cloud_firestore:
    dependency: transitive
    description:
      name: cloud_firestore
      sha256: "6b5d0ca6b62830ca5bcf98c5e0c882df32dea3cb523b635db3626333e1c29090"
      url: "https://pub.dev"
    source: hosted
    version: "5.6.5"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      sha256: "149c7d2d634178aff8e25eba6cbbbc76bd92f37e0e63727a31952c72bbcb77fb"
      url: "https://pub.dev"
    source: hosted
    version: "6.6.5"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      sha256: "05a3c02a7edb3fadeb3f14f491c3a0bbad3ea2c9f22842acbf1d73bceeb93e77"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.5"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "0ec10bf4a89e4c613960bf1e8b42c64127021740fb21640c29c909826a5eea3e"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.1"
  collection:
    dependency: "direct main"
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  country_code_picker:
    dependency: "direct main"
    description:
      name: country_code_picker
      sha256: "92818885f0e47486539f80463b66f649970506a91dd3c0731ca3ba5308324a4d"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  country_pickers:
    dependency: "direct main"
    description:
      name: country_pickers
      sha256: b10f6618fa64fbba02ffc4ad1b84dc0ca071cc206e5376de1698bddd980b355a
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: "direct main"
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "7306ab8a2359a48d22310ad823521d723acfed60ee1f7e37388e8986853b6820"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.8"
  delete_un_used_assets:
    dependency: "direct dev"
    description:
      name: delete_un_used_assets
      sha256: "97d7658831031b5c72871d82c9c30760e97fd42170cbd9af4f35e3e754d8cc95"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  devicelocale:
    dependency: "direct main"
    description:
      name: devicelocale
      sha256: c198785f937840c207bd1765769faf4fda9a611507c0f11041a860a681f3adfa
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0+1"
  dio_web_adapter:
    dependency: "direct overridden"
    description:
      name: dio_web_adapter
      sha256: "7586e476d70caecaf1686d21eee7247ea43ef5c345eab9e0cc3583ff13378d78"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  dotted_decoration:
    dependency: "direct main"
    description:
      name: dotted_decoration
      sha256: a5c5771367690b4f64ebfa7911954ab472b9675f025c373f514e32ac4bb81d5e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  easy_debounce:
    dependency: "direct main"
    description:
      name: easy_debounce
      sha256: f082609cfb8f37defb9e37fc28bc978c6712dedf08d4c5a26f820fa10165a236
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      sha256: "62d9aa4670cc2a8798bab89b39fc71b6dfbacf615de6cf5001fb39f7e4a996a2"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: "567c64b3cb4cf82397aac55f4f0cbd3ca20d77c6c03bedbc4ceaddc08904aef7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.7"
  event_bus:
    dependency: transitive
    description:
      name: event_bus
      sha256: "1a55e97923769c286d295240048fc180e7b0768902c3c2e869fe059aafa15304"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  extended_image:
    dependency: "direct main"
    description:
      name: extended_image
      sha256: "69d4299043334ecece679996e47d0b0891cd8c29d8da0034868443506f1d9a78"
      url: "https://pub.dev"
    source: hosted
    version: "8.3.1"
  extended_image_library:
    dependency: transitive
    description:
      name: extended_image_library
      sha256: e61dafd94400fff6ef7ed1523d445ff3af137f198f3228e4a3107bc5b4bec5d1
      url: "https://pub.dev"
    source: hosted
    version: "4.0.6"
  facebook_auth_desktop:
    dependency: transitive
    description:
      name: facebook_auth_desktop
      sha256: e6cc4d6f50a1d67d99e7dac7d77a40fe27122496e224cb708ae168d7d9aac0ac
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "289279317b4b16eb2bb7e271abccd4bf84ec9bdcbe999e278a94b804f5630418"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "54cbbd957e1156d29548c7d9b9ec0c0ebb6de0a90452198683a7d23aed617a33"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+2"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "271ab9986df0c135d45c3cdb6bd0faa5db6f4976d3e4b437cf7d0f258d941bfc"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4+2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "320fcfb6f33caa90f0b58380489fc5ac05d99ee94b61aa96ec2bff0ba81d3c2b"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+4"
  firebase_analytics:
    dependency: transitive
    description:
      name: firebase_analytics
      sha256: "81a582e9348216fcf6b30878487369325bf78b8ddd752ed176949c8e4fd4aaac"
      url: "https://pub.dev"
    source: hosted
    version: "11.4.4"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "5ae7bd4a551b67009cd0676f5407331b202eaf16e0a80dcf7b40cd0a34a18746"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.4"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "15fd7459fea2a00958dbf9b86cd8ad14d3ce2db13950308af7c7717e89ccc5c2"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.10+10"
  firebase_auth:
    dependency: transitive
    description:
      name: firebase_auth
      sha256: "91587615d7d9165c65a030426e3cf40bbec37c486f52ff654af17aba5be3d208"
      url: "https://pub.dev"
    source: hosted
    version: "5.5.1"
  firebase_auth_platform_interface:
    dependency: transitive
    description:
      name: firebase_auth_platform_interface
      sha256: "1dcf1dbdd90fe97fa37ab3631b561bf584adb88f6be0b0dd915fff799ad53192"
      url: "https://pub.dev"
    source: hosted
    version: "7.6.1"
  firebase_auth_web:
    dependency: transitive
    description:
      name: firebase_auth_web
      sha256: "3774cb13547e28b180fed2a5e696b4b36f97f4b1fadc7b04a0200e5009344d98"
      url: "https://pub.dev"
    source: hosted
    version: "5.14.1"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: f4d8f49574a4e396f34567f3eec4d38ab9c3910818dec22ca42b2a467c685d8b
      url: "https://pub.dev"
    source: hosted
    version: "3.12.1"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: d7253d255ff10f85cfd2adaba9ac17bae878fa3ba577462451163bd9f1d1f0bf
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: faa5a76f6380a9b90b53bc3bdcb85bc7926a382e0709b9b5edac9f7746651493
      url: "https://pub.dev"
    source: hosted
    version: "2.21.1"
  firebase_dynamic_links:
    dependency: transitive
    description:
      name: firebase_dynamic_links
      sha256: "4354813cecedf897b08ccb57ca111d127d6e5960e93af272df62af8cd1097d36"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  firebase_dynamic_links_platform_interface:
    dependency: transitive
    description:
      name: firebase_dynamic_links_platform_interface
      sha256: "0528ce67b3e7b39f6bcb9a243f26102ea3fe8a5f5df3624bc35e306f46f4423c"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.7+4"
  firebase_messaging:
    dependency: transitive
    description:
      name: firebase_messaging
      sha256: "5fc345c6341f9dc69fd0ffcbf508c784fd6d1b9e9f249587f30434dd8b6aa281"
      url: "https://pub.dev"
    source: hosted
    version: "15.2.4"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: a935924cf40925985c8049df4968b1dde5c704f570f3ce380b31d3de6990dd94
      url: "https://pub.dev"
    source: hosted
    version: "4.6.4"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: fafebf6a1921931334f3f10edb5037a5712288efdd022881e2d093e5654a2fd4
      url: "https://pub.dev"
    source: hosted
    version: "3.10.4"
  firebase_remote_config:
    dependency: transitive
    description:
      name: firebase_remote_config
      sha256: "908fe47d1aea2dbb6c6f3ef93ae884b34bafddf696495949f75c981a47f6704e"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.2"
  firebase_remote_config_platform_interface:
    dependency: transitive
    description:
      name: firebase_remote_config_platform_interface
      sha256: "0914e2680235cca3e0c88ad98ff387d38c5455ec8d24ad70649a15e6a872295b"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.2"
  firebase_remote_config_web:
    dependency: transitive
    description:
      name: firebase_remote_config_web
      sha256: "7fd610cf20077fb1b3383573968e3477864272f540788b6da24e4fab395f68bb"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.2"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flash:
    dependency: "direct main"
    description:
      name: flash
      sha256: "1bb5a53158d7834ce0db8b15a0b948ee369698e59c907b751035c3a787f347ac"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_animate:
    dependency: "direct main"
    description:
      name: flutter_animate
      sha256: "7c8a6594a9252dad30cc2ef16e33270b6248c4dedc3b3d06c86c4f3f4dc05ae5"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.0"
  flutter_async_autocomplete:
    dependency: "direct main"
    description:
      name: flutter_async_autocomplete
      sha256: "573c1480c8430ced699199c7a6fa9a4f3876ee908ee947bc7b9b5b0bf6c42bc0"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  flutter_bloc:
    dependency: transitive
    description:
      name: flutter_bloc
      sha256: b594505eac31a0518bdcb4b5b79573b8d9117b193cc80cc12e17d639b10aa27a
      url: "https://pub.dev"
    source: hosted
    version: "8.1.6"
  flutter_cache_manager:
    dependency: "direct main"
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_custom_clippers:
    dependency: "direct main"
    description:
      name: flutter_custom_clippers
      sha256: "473e3daf61c2a6cee0ad137393259a25223239d519a131c7ec1cac04d06e5407"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  flutter_direct_caller_plugin:
    dependency: "direct main"
    description:
      name: flutter_direct_caller_plugin
      sha256: "0dc32a2cb351f41ea633a9c9e6be5eb5239197f390e519f3a991bda794bba0cd"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  flutter_facebook_auth:
    dependency: "direct main"
    description:
      name: flutter_facebook_auth
      sha256: "4958d39b62791d8f08c429b5c296e9e27f850e3385d63ebc9fe7b69f2c243c6c"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.1"
  flutter_facebook_auth_platform_interface:
    dependency: transitive
    description:
      name: flutter_facebook_auth_platform_interface
      sha256: "86630c4dbba1c20fba26ea9e59ad0d48f5ff59e7373cacd36f916160186f9ce9"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_facebook_auth_web:
    dependency: transitive
    description:
      name: flutter_facebook_auth_web
      sha256: "22dca8091409309ad85b9f430fbd8f57b686276979da5195e7e97587352567ce"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_hooks:
    dependency: "direct main"
    description:
      name: flutter_hooks
      sha256: b772e710d16d7a20c0740c4f855095026b31c7eb5ba3ab67d2bd52021cd9461d
      url: "https://pub.dev"
    source: hosted
    version: "0.21.2"
  flutter_inappwebview:
    dependency: "direct main"
    description:
      name: flutter_inappwebview
      sha256: "80092d13d3e29b6227e25b67973c67c7210bd5e35c4b747ca908e31eb71a46d5"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.5"
  flutter_inappwebview_android:
    dependency: transitive
    description:
      name: flutter_inappwebview_android
      sha256: "62557c15a5c2db5d195cb3892aab74fcaec266d7b86d59a6f0027abd672cddba"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  flutter_inappwebview_internal_annotations:
    dependency: transitive
    description:
      name: flutter_inappwebview_internal_annotations
      sha256: "787171d43f8af67864740b6f04166c13190aa74a1468a1f1f1e9ee5b90c359cd"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  flutter_inappwebview_ios:
    dependency: transitive
    description:
      name: flutter_inappwebview_ios
      sha256: "5818cf9b26cf0cbb0f62ff50772217d41ea8d3d9cc00279c45f8aabaa1b4025d"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_macos:
    dependency: transitive
    description:
      name: flutter_inappwebview_macos
      sha256: c1fbb86af1a3738e3541364d7d1866315ffb0468a1a77e34198c9be571287da1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_platform_interface:
    dependency: transitive
    description:
      name: flutter_inappwebview_platform_interface
      sha256: cf5323e194096b6ede7a1ca808c3e0a078e4b33cc3f6338977d75b4024ba2500
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0+1"
  flutter_inappwebview_web:
    dependency: transitive
    description:
      name: flutter_inappwebview_web
      sha256: "55f89c83b0a0d3b7893306b3bb545ba4770a4df018204917148ebb42dc14a598"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_windows:
    dependency: transitive
    description:
      name: flutter_inappwebview_windows
      sha256: "8b4d3a46078a2cdc636c4a3d10d10f2a16882f6be607962dbfff8874d1642055"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      sha256: bfa04787c85d80ecb3f8777bde5fc10c3de809240c48fa061a2c2bf15ea5211c
      url: "https://pub.dev"
    source: hosted
    version: "0.14.3"
  flutter_linkify:
    dependency: "direct main"
    description:
      name: flutter_linkify
      sha256: "74669e06a8f358fee4512b4320c0b80e51cffc496607931de68d28f099254073"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: e2a421b7e59244faef694ba7b30562e489c2b489866e505074eb005cd7060db7
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  flutter_localization:
    dependency: "direct main"
    description:
      name: flutter_localization
      sha256: "972eb337dcc27480e575accf9eadcdd80f52755ba05efcfdb585b869f29ad3d6"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_native_splash:
    dependency: "direct dev"
    description:
      name: flutter_native_splash
      sha256: "7062602e0dbd29141fb8eb19220b5871ca650be5197ab9c1f193a28b17537bc7"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.4"
  flutter_plugin_android_lifecycle:
    dependency: "direct overridden"
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "5a1e6fb2c0561958d7e4c33574674bda7b77caaca7a33b758876956f2902eea3"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.27"
  flutter_rating_bar:
    dependency: transitive
    description:
      name: flutter_rating_bar
      sha256: d2af03469eac832c591a1eba47c91ecc871fe5708e69967073c043b2d775ed93
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_secure_storage:
    dependency: "direct main"
    description:
      name: flutter_secure_storage
      sha256: "9cad52d75ebc511adfae3d447d5d13da15a55a92c9410e50f67335b6d21d16ea"
      url: "https://pub.dev"
    source: hosted
    version: "9.2.4"
  flutter_secure_storage_linux:
    dependency: transitive
    description:
      name: flutter_secure_storage_linux
      sha256: bf7404619d7ab5c0a1151d7c4e802edad8f33535abfbeff2f9e1fe1274e2d705
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: "6c0a2795a2d1de26ae202a0d78527d163f4acbb11cde4c75c670f3a0fc064247"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: cf91ad32ce5adef6fba4d736a542baca9daf3beac4db2d04be350b87f69ac4a8
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: f4ebff989b4f07b2656fb16b47852c0aab9fed9b4ec1c70103368337bc1886a9
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: b20b07cb5ed4ed74fc567b78a72936203f587eba460af1df11281c9326cd3709
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  flutter_shaders:
    dependency: transitive
    description:
      name: flutter_shaders
      sha256: "34794acadd8275d971e02df03afee3dee0f98dbfb8c4837082ad0034f612a3e2"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  flutter_spinkit:
    dependency: "direct main"
    description:
      name: flutter_spinkit
      sha256: b39c753e909d4796906c5696a14daf33639a76e017136c8d82bf3e620ce5bb8e
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "19e7abb550c96fbfeb546b23f3ff356ee7c59a019a651f8f102a4ba9b7349395"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: c200fd79c918a40c5cd50ea0877fa13f81bdaf6f0a5d3dbcc2a13e3285d6aa1b
      url: "https://pub.dev"
    source: hosted
    version: "2.0.17"
  flutter_swiper_null_safety:
    dependency: "direct main"
    description:
      path: "."
      ref: "18e8d2d642ff9e0013fc4477c6d64c52732b463a"
      resolved-ref: "18e8d2d642ff9e0013fc4477c6d64c52732b463a"
      url: "https://github.com/inspireui/flutter_swiper_null_safety"
    source: git
    version: "1.0.2"
  flutter_test:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html_core:
    dependency: "direct main"
    description:
      name: flutter_widget_from_html_core
      sha256: f77ea1aa1ba29a38fcce04483f44f12382f541b9e8c2150df37166c23bbbd30f
      url: "https://pub.dev"
    source: hosted
    version: "0.16.0"
  flutter_zoom_drawer:
    dependency: "direct main"
    description:
      name: flutter_zoom_drawer
      sha256: "5a3708548868463fb36e0e3171761ab7cb513df88d2f14053802812d2e855060"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  flux_firebase:
    dependency: "direct main"
    description:
      path: "packages/flux_firebase"
      relative: true
    source: path
    version: "1.0.0+1"
  font_awesome_flutter:
    dependency: "direct main"
    description:
      name: font_awesome_flutter
      sha256: d3a89184101baec7f4600d58840a764d2ef760fe1c5a20ef9e6b0e9b24a07a3a
      url: "https://pub.dev"
    source: hosted
    version: "10.8.0"
  freezed:
    dependency: "direct dev"
    description:
      name: freezed
      sha256: "57247f692f35f068cae297549a46a9a097100685c6780fe67177503eea5ed4e5"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.7"
  freezed_annotation:
    dependency: "direct main"
    description:
      name: freezed_annotation
      sha256: c3fd9336eb55a38cc1bbd79ab17573113a8deccd0ecbbf926cca3c62803b5c2d
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  fwfh_cached_network_image:
    dependency: "direct main"
    description:
      name: fwfh_cached_network_image
      sha256: "8f4896109ff3e42424ccacf9058ba3afe5d575b58946c8ac646ac85ae882ce23"
      url: "https://pub.dev"
    source: hosted
    version: "0.16.0"
  fwfh_svg:
    dependency: "direct main"
    description:
      name: fwfh_svg
      sha256: "82f3eb378186fe39b3e2e01ed48a1830d34b0b9a237d951077e74ff0d99e2ac3"
      url: "https://pub.dev"
    source: hosted
    version: "0.16.0"
  fwfh_webview:
    dependency: "direct main"
    description:
      name: fwfh_webview
      sha256: "894aa7d98ebdc2d86d79ac2309173043dec7f102575de87bf9626ddb26104e49"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  get:
    dependency: transitive
    description:
      name: get
      sha256: c79eeb4339f1f3deffd9ec912f8a923834bec55f7b49c9e882b8fef2c139d425
      url: "https://pub.dev"
    source: hosted
    version: "4.7.2"
  get_it:
    dependency: "direct main"
    description:
      name: get_it
      sha256: e6017ce7fdeaf218dc51a100344d8cb70134b80e28b760f8bb23c242437bafd7
      url: "https://pub.dev"
    source: hosted
    version: "7.6.7"
  get_storage:
    dependency: "direct main"
    description:
      name: get_storage
      sha256: "39db1fffe779d0c22b3a744376e86febe4ade43bf65e06eab5af707dc84185a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: c3f1ee72c96f8f78935e18aa8cecced9ab132419e8625dc187e1c2408efc20de
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  gms_check:
    dependency: "direct main"
    description:
      path: "packages/gms_check"
      relative: true
    source: path
    version: "1.0.2"
  google_fonts:
    dependency: "direct main"
    description:
      name: google_fonts
      sha256: f0b8d115a13ecf827013ec9fc883390ccc0e87a96ed5347a3114cac177ef18e8
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  google_identity_services_web:
    dependency: transitive
    description:
      name: google_identity_services_web
      sha256: "55580f436822d64c8ff9a77e37d61f5fb1e6c7ec9d632a43ee324e2a05c3c6c9"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3"
  google_maps:
    dependency: transitive
    description:
      name: google_maps
      sha256: "4d6e199c561ca06792c964fa24b2bac7197bf4b401c2e1d23e345e5f9939f531"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.1"
  google_maps_flutter:
    dependency: "direct main"
    description:
      name: google_maps_flutter
      sha256: "621125e35e81ca39ef600e45243d2be93167e61def72bc7207b0c4a635c58506"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.1"
  google_maps_flutter_android:
    dependency: transitive
    description:
      name: google_maps_flutter_android
      sha256: "3b3f55d6b4f2bde6bbe80dca0bf8d228313005c9ce8a97a1d24257600d8c92e5"
      url: "https://pub.dev"
    source: hosted
    version: "2.14.14"
  google_maps_flutter_ios:
    dependency: transitive
    description:
      name: google_maps_flutter_ios
      sha256: "6f798adb0aa1db5adf551f2e39e24bd06c8c0fbe4de912fb2d9b5b3f48147b02"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.2"
  google_maps_flutter_platform_interface:
    dependency: transitive
    description:
      name: google_maps_flutter_platform_interface
      sha256: "970c8f766c02909c7be282dea923c971f83a88adaf07f8871d0aacebc3b07bb2"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.1"
  google_maps_flutter_web:
    dependency: transitive
    description:
      name: google_maps_flutter_web
      sha256: bbeb93807a34bfeebdb7ace506bd2bc400a3915dc96736254fea721eb264caa0
      url: "https://pub.dev"
    source: hosted
    version: "0.5.11"
  google_mobile_ads:
    dependency: "direct main"
    description:
      name: google_mobile_ads
      sha256: "0d4a3744b5e8ed1b8be6a1b452d309f811688855a497c6113fc4400f922db603"
      url: "https://pub.dev"
    source: hosted
    version: "5.3.1"
  google_sign_in:
    dependency: "direct main"
    description:
      name: google_sign_in
      sha256: "0b8787cb9c1a68ad398e8010e8c8766bfa33556d2ab97c439fb4137756d7308f"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  google_sign_in_android:
    dependency: "direct overridden"
    description:
      name: google_sign_in_android
      sha256: "4e52c64366bdb3fe758f683b088ee514cc7a95e69c52b5ee9fc5919e1683d21b"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  google_sign_in_ios:
    dependency: transitive
    description:
      name: google_sign_in_ios
      sha256: "29cd125f58f50ceb40e8253d3c0209e321eee3e5df16cd6d262495f7cad6a2bd"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.1"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      sha256: "5f6f79cf139c197261adb6ac024577518ae48fdff8e53205c5373b5f6430a8aa"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      sha256: "460547beb4962b7623ac0fb8122d6b8268c951cf0b646dd150d60498430e4ded"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.4+4"
  googleapis_auth:
    dependency: "direct main"
    description:
      name: googleapis_auth
      sha256: befd71383a955535060acde8792e7efc11d2fccd03dd1d3ec434e85b68775938
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: "741bbf84165310a68ff28fe9e727332eef1407342fca52759cb21ad8177bb8d0"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  hive:
    dependency: "direct main"
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  hive_flutter:
    dependency: "direct main"
    description:
      name: hive_flutter
      sha256: dca1da446b1d808a51689fb5d0c6c9510c0a2ba01e22805d492c73b68e33eecc
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  html:
    dependency: "direct main"
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  html_unescape:
    dependency: "direct main"
    description:
      name: html_unescape
      sha256: "15362d7a18f19d7b742ef8dcb811f5fd2a2df98db9f80ea393c075189e0b61e3"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  http:
    dependency: "direct main"
    description:
      name: http
      sha256: fe7ab022b76f3034adc518fb6ea04a82387620e19977665ea18d30a1cf43442f
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  http_auth:
    dependency: "direct main"
    description:
      name: http_auth
      sha256: b7625acba2987fa69140d9600c679819f33227d665f525fbb2f394e08cf917d1
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  http_client_helper:
    dependency: transitive
    description:
      name: http_client_helper
      sha256: "8a9127650734da86b5c73760de2b404494c968a3fd55602045ffec789dac3cb1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: aa6199f908078bb1c5efb8d8638d4ae191aac11b311132c3ef48ce352fb52ef8
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "178d74305e7866013777bab2c3d8726205dc5a4dd935297175b19a23a2e66571"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: "13d3349ace88f12f4a0d175eb5c12dcdd39d35c4c109a8a13dfeb6d0bd9e31c3"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.3"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "8bd392ba8b0c8957a157ae0dc9fcf48c58e6c20908d5880aea1d79734df090e9"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+22"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "05da758e67bc7839e886b3959848aa6b44ff123ab4b28f67891008afe8ef9100"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+2"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "1b90ebbd9dcf98fb6c1d01427e49a55bd96b5d67b8c67cf955d60a5de74207c1"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "886d57f0be73c4b140004e78b9f28a8914a09e50c2d816bdd0520051a71236a0"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  in_app_update:
    dependency: "direct main"
    description:
      name: in_app_update
      sha256: b6ccb757281a96a4b18536f68fe2567aeca865134218719364212da8fe94615c
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  infinite_carousel:
    dependency: "direct main"
    description:
      name: infinite_carousel
      sha256: "572967b028f4803e5f4be9adee5760b6c4834eb6912b0f2292f2d93a51912ba0"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  inspireui:
    dependency: "direct main"
    description:
      path: "packages/inspireui"
      relative: true
    source: path
    version: "2.2.7"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3df61194eb431efc39c4ceba583b95633a403f46c9fd341e550ce0bfa50e9aa5"
      url: "https://pub.dev"
    source: hosted
    version: "0.20.2"
  intl_phone_number_input:
    dependency: "direct main"
    description:
      path: "."
      ref: "1a3fa368a009eac3cf02a91052956981da10e725"
      resolved-ref: "1a3fa368a009eac3cf02a91052956981da10e725"
      url: "https://github.com/inspireui/intl_phone_number_input"
    source: git
    version: "0.7.3+1"
  intrinsic_grid_view:
    dependency: "direct main"
    description:
      name: intrinsic_grid_view
      sha256: c86b77d569af3b2420fcfd3dbcd6ec1e83dab57b08e49acb05e2d257510652d2
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  intro_slider:
    dependency: "direct main"
    description:
      name: intro_slider
      sha256: eb43fefa27b0655edebc3e7fe7ff320cb5996a4e64649d79969c7099105fe52f
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  io:
    dependency: transitive
    description:
      name: io
      sha256: dfd5a80599cf0165756e3181807ed3e77daf6dd4137caaad72d0b7931597650b
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: "direct main"
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      sha256: c2fcb3920cf2b6ae6845954186420fca40bc0a8abcc84903b7801f17d7050d7c
      url: "https://pub.dev"
    source: hosted
    version: "6.9.0"
  jumping_dot:
    dependency: "direct main"
    description:
      name: jumping_dot
      sha256: a2f755140da73d018bf5198acd83cdd2898a393e5ea45ee94250b9ba44566d1c
      url: "https://pub.dev"
    source: hosted
    version: "0.0.6"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "6bb818ecbdffe216e81182c2f0714a2e62b593f4a4f13098713ff1685dfb6ab0"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.9"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  libphonenumber_platform_interface:
    dependency: transitive
    description:
      name: libphonenumber_platform_interface
      sha256: f801f6c65523f56504b83f0890e6dad584ab3a7507dca65fec0eed640afea40f
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2"
  libphonenumber_plugin:
    dependency: transitive
    description:
      name: libphonenumber_plugin
      sha256: c615021d9816fbda2b2587881019ed595ecdf54d999652d7e4cce0e1f026368c
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3"
  libphonenumber_web:
    dependency: transitive
    description:
      name: libphonenumber_web
      sha256: "8186f420dbe97c3132283e52819daff1e55d60d6db46f7ea5ac42f42a28cc2ef"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.2"
  linkify:
    dependency: transitive
    description:
      name: linkify
      sha256: "4139ea77f4651ab9c315b577da2dd108d9aa0bd84b5d03d33323f1970c645832"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: cbf8d4b858bb0134ef3ef87841abdf8d63bfc255c266b7bf6b39daa1085c4290
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  local_auth:
    dependency: "direct main"
    description:
      name: local_auth
      sha256: "434d854cf478f17f12ab29a76a02b3067f86a63a6d6c4eb8fbfdcfe4879c1b7b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  local_auth_android:
    dependency: "direct main"
    description:
      name: local_auth_android
      sha256: "0abe4e72f55c785b28900de52a2522c86baba0988838b5dc22241b072ecccd74"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.48"
  local_auth_darwin:
    dependency: transitive
    description:
      name: local_auth_darwin
      sha256: "630996cd7b7f28f5ab92432c4b35d055dd03a747bc319e5ffbb3c4806a3e50d2"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.3"
  local_auth_platform_interface:
    dependency: transitive
    description:
      name: local_auth_platform_interface
      sha256: "1b842ff177a7068442eae093b64abe3592f816afd2a533c0ebcdbe40f9d2075a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.10"
  local_auth_windows:
    dependency: transitive
    description:
      name: local_auth_windows
      sha256: bc4e66a29b0fdf751aafbec923b5bed7ad6ed3614875d8151afe2578520b2ab5
      url: "https://pub.dev"
    source: hosted
    version: "1.0.11"
  location:
    dependency: "direct main"
    description:
      name: location
      sha256: "06be54f682c9073cbfec3899eb9bc8ed90faa0e17735c9d9fa7fe426f5be1dd1"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  location_platform_interface:
    dependency: transitive
    description:
      name: location_platform_interface
      sha256: "8aa1d34eeecc979d7c9fe372931d84f6d2ebbd52226a54fe1620de6fdc0753b1"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  location_web:
    dependency: transitive
    description:
      name: location_web
      sha256: ec484c66e8a4ff1ee5d044c203f4b6b71e3a0556a97b739a5bc9616de672412b
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  logger:
    dependency: transitive
    description:
      name: logger
      sha256: be4b23575aac7ebf01f225a241eb7f6b5641eeaf43c6a8613510fc2f8cf187d1
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: c5fa04a80a620066c15cf19cc44773e19e9b38e989ff23ea32e5903ef1015950
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  macros:
    dependency: transitive
    description:
      name: macros
      sha256: "1d9e801cd66f7ea3663c45fc708450db1fa57f988142c64289142c9b7ee80656"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3-main.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  meta_seo:
    dependency: "direct main"
    description:
      name: meta_seo
      sha256: fb1984710baca873da6ca9cd411d869bc56370f0cfb8e140dd42827424c681b5
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  multiple_localization:
    dependency: "direct main"
    description:
      path: "."
      ref: "48b65fa88ed468331d56ee86a102d808596c0a50"
      resolved-ref: "48b65fa88ed468331d56ee86a102d808596c0a50"
      url: "https://github.com/inspireui/flutter_multiple_localization"
    source: git
    version: "0.3.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  new_version_plus:
    dependency: "direct main"
    description:
      name: new_version_plus
      sha256: e0d8027223488cc7f7c78f6ff286f1b5d9808f88814aecfa0bb12254d25f48ca
      url: "https://pub.dev"
    source: hosted
    version: "0.0.11"
  notification_permissions:
    dependency: "direct main"
    description:
      path: "packages/notification_permissions"
      relative: true
    source: path
    version: "0.6.1"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "34faa6639a78c7e3cbe79be6f9f96535867e879748ade7d17c9b1ae7536293bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  omni_datetime_picker:
    dependency: "direct main"
    description:
      name: omni_datetime_picker
      sha256: db3d92e23513a34655adf7706f258c1b6049984aed0b78f8f274a6222c71360e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: f096c55ebb7deb7e384101542bfba8c52696c1b56fca2eb62827989ef2353bbc
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: "7e76fad405b3e4016cd39d08f455a4eb5199723cf594cd1b8916d47140d93017"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  path:
    dependency: "direct main"
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "0ca7359dad67fd7063cb2892ab0c0737b2daafd807cf1acecd62374c8fae6c12"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.16"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  paypal_payment:
    dependency: "direct main"
    description:
      name: paypal_payment
      sha256: "8ed4adaafd3daf8ec12817cd8a67ca32bc8d84b7357b7b888f5cf60078bb14eb"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1+1"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "59adad729136f01ea9e35a48f5d1395e25cba6cea552249ddbe9cf950f5d7849"
      url: "https://pub.dev"
    source: hosted
    version: "11.4.0"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: d3971dcdd76182a0c198c096b5db2f0884b0d4196723d21a866fc4cdea057ebc
      url: "https://pub.dev"
    source: hosted
    version: "12.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: f84a188e79a35c687c132a0a0556c254747a08561e99ab933f12f6ca71ef3c98
      url: "https://pub.dev"
    source: hosted
    version: "9.4.6"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: "38f000e83355abb3392140f6bc3030660cfaef189e1f87824facb76300b4ff24"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3+5"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: eb99b295153abce5d683cac8c02e22faab63e50679b937fa1bf67d58bb282878
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "07c8f0b1913bcde1ff0d26e57ace2f3012ccbf2b204e070290dad3bb22797646"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  photo_manager:
    dependency: "direct overridden"
    description:
      path: "packages/photo_manager"
      relative: true
    source: path
    version: "3.6.4"
  photo_manager_image_provider:
    dependency: transitive
    description:
      name: photo_manager_image_provider
      sha256: b6015b67b32f345f57cf32c126f871bced2501236c405aafaefa885f7c821e4f
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  pin_code_fields:
    dependency: "direct main"
    description:
      name: pin_code_fields
      sha256: "4c0db7fbc889e622e7c71ea54b9ee624bb70c7365b532abea0271b17ea75b729"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.1"
  pin_input_text_field:
    dependency: transitive
    description:
      name: pin_input_text_field
      sha256: f45683032283d30b670ec343781660655e3e1953438b281a0bc6e2d358486236
      url: "https://pub.dev"
    source: hosted
    version: "4.5.2"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.dev"
    source: hosted
    version: "3.9.1"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  provider:
    dependency: "direct main"
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "5bfcf68ca79ef689f8990d1160781b4bad40a3bd5e5218ad4076ddb7f4081585"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "0560ba233314abbed0a48a2956f7f022cce7c3e1e73df540277da7544cad4082"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  pull_to_refresh:
    dependency: "direct main"
    description:
      path: "."
      ref: "24df296e282cd8b4ea8d3a5e171f4156a0295d1b"
      resolved-ref: "24df296e282cd8b4ea8d3a5e171f4156a0295d1b"
      url: "https://github.com/inspireui/flutter_pull_to_refresh"
    source: git
    version: "2.0.0"
  qr_code_scanner_plus:
    dependency: "direct main"
    description:
      name: qr_code_scanner_plus
      sha256: "39696b50d277097ee4d90d4292de36f38c66213a4f5216a06b2bdd2b63117859"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.10+1"
  quickalert:
    dependency: "direct main"
    description:
      path: "packages/quick_alert"
      relative: true
    source: path
    version: "1.1.0"
  quiver:
    dependency: "direct main"
    description:
      name: quiver
      sha256: b1c1ac5ce6688d77f65f3375a9abb9319b3cb32486bdc7a1e0fdf004d7ba4e47
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  random_string:
    dependency: "direct main"
    description:
      name: random_string
      sha256: "03b52435aae8cbdd1056cf91bfc5bf845e9706724dd35ae2e99fa14a1ef79d02"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  rate_my_app:
    dependency: "direct main"
    description:
      name: rate_my_app
      sha256: e448dc27f8e821824fe7f67c2dba22f67b18de08054efa746defd1cd1657d882
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  readmore:
    dependency: "direct main"
    description:
      name: readmore
      sha256: "99c2483202f7c7e98c50834d72be2b119aefecf4497ca1960ae9d5f418eb1481"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  rect_getter:
    dependency: "direct main"
    description:
      name: rect_getter
      sha256: "1c71e4ab5fb7d83d2b14a8338f979bc9ae57eefba786e7fbc91f6746dac2193d"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  responsive_builder:
    dependency: "direct main"
    description:
      name: responsive_builder
      sha256: a38ba9ba86c9daf08904674553034b651377b1d685d10ee450d8350ae51f76ec
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  restart_app:
    dependency: "direct main"
    description:
      name: restart_app
      sha256: "00d5ec3e9de871cedbe552fc41e615b042b5ec654385e090e0983f6d02f655ed"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  sanitize_html:
    dependency: transitive
    description:
      name: sanitize_html
      sha256: "12669c4a913688a26555323fb9cec373d8f9fbe091f2d01c40c723b33caa8989"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  scroll_to_index:
    dependency: "direct main"
    description:
      name: scroll_to_index
      sha256: b707546e7500d9f070d63e5acf74fd437ec7eeeb68d3412ef7b0afada0b4f176
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  scrollable_positioned_list:
    dependency: "direct main"
    description:
      name: scrollable_positioned_list
      sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.8"
  shared_preferences:
    dependency: "direct overridden"
    description:
      name: shared_preferences
      sha256: "846849e3e9b68f3ef4b60c60cf4b3e02e9321bc7f4d8c4692cf87ffa82fc8a3a"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "3ec7210872c4ba945e3244982918e502fa2bfb5230dff6832459ca0e1879b7ad"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.8"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "6a52cfcdaeac77cad8c97b539ff688ccfc458c007b4db12be584fbe5c0e49e03"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: c49bd060261c9a3f0ff445892695d6212ff603ef3115edbb448509d407600019
      url: "https://pub.dev"
    source: hosted
    version: "2.4.3"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: e7dd780a7ffb623c57850b33f43309312fc863fb6aa3d276a754bb299839ef12
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  smooth_page_indicator:
    dependency: "direct main"
    description:
      name: smooth_page_indicator
      sha256: "725bc638d5e79df0c84658e1291449996943f93bacbc2cec49963dbbab48d8ae"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  sms_autofill:
    dependency: "direct main"
    description:
      name: sms_autofill
      sha256: c65836abe9c1f62ce411bb78d5546a09ece4297558070b1bd871db1db283aaf9
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "14658ba5f669685cd3d63701d01b31ea748310f7ab854e471962670abcf57832"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "86d247119aedce8e63f4751bd9626fc9613255935558447569ad42f9f5b48b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.5"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: "direct overridden"
    description:
      name: sqflite
      sha256: e2297b1da52f127bc7a3da11439985d9b536f75070f3325e62ada69a5c585d03
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  sqflite_android:
    dependency: transitive
    description:
      name: sqflite_android
      sha256: "2b3070c5fa881839f8b402ee4a39c1b4d561704d4ebbbcfb808a119bc2a1701b"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "84731e8bfd8303a3389903e01fb2141b6e59b5973cacbb0929021df08dddbe8b"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.5"
  sqflite_darwin:
    dependency: transitive
    description:
      name: sqflite_darwin
      sha256: "279832e5cde3fe99e8571879498c9211f3ca6391b0d818df4e17d9fff5c6ccb3"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  sqflite_platform_interface:
    dependency: transitive
    description:
      name: sqflite_platform_interface
      sha256: "8dd4515c7bdcae0a785b0062859336de775e8c65db81ae33dd5445f35be61920"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  sticky_headers:
    dependency: "direct main"
    description:
      name: sticky_headers
      sha256: "9b3dd2cb0fd6a7038170af3261f855660cbb241cb56c501452cb8deed7023ede"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0+2"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "0669c70faae6270521ee4f05bffd2919892d42d1276e6c495be80174b6bc0ef6"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  the_apple_sign_in:
    dependency: "direct main"
    description:
      name: the_apple_sign_in
      sha256: "52163df2619e5461f63559002eee171fe78ebab3af6f6f34c4026df5f15859ad"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  timeago:
    dependency: "direct main"
    description:
      name: timeago
      sha256: "054cedf68706bb142839ba0ae6b135f6b68039f0b8301cbe8784ae653d5ff8de"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.0"
  timezone:
    dependency: "direct main"
    description:
      name: timezone
      sha256: "1cfd8ddc2d1cfd836bc93e67b9be88c3adaeca6f40a00ca999104c30693cdca0"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "62ee18aca144e4a9f29d212f5a4c6a053be252b895ab14b5821996cff4ed90fe"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  translator:
    dependency: "direct main"
    description:
      name: translator
      sha256: "8f5e56d0ffb8f493b23ad0e4f824c17e5f43d45997e33b7c7b689c7a33cf3b06"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3+1"
  transparent_image:
    dependency: "direct main"
    description:
      name: transparent_image
      sha256: e8991d955a2094e197ca24c645efec2faf4285772a4746126ca12875e54ca02f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  universal_html:
    dependency: "direct main"
    description:
      name: universal_html
      sha256: "56536254004e24d9d8cfdb7dbbf09b74cf8df96729f38a2f5c238163e3d58971"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  universal_platform:
    dependency: "direct main"
    description:
      name: universal_platform
      sha256: "64e16458a0ea9b99260ceb5467a214c1f298d647c659af1bff6d3bf82536b1ec"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: "direct overridden"
    description:
      name: url_launcher_android
      sha256: "1d0eae19bd7606ef60fe69ef3b312a437a16549476c42321d5dc1506c9ca3bf4"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.15"
  url_launcher_ios:
    dependency: "direct main"
    description:
      name: url_launcher_ios
      sha256: e43b677296fadce447e987a2f519dcf5f6d1e527dc35d01ffab4fff5b8a7063e
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "3ba963161bd0fe395917ba881d320b9c4f6dd3c4a233da62ab18a5025c85f1e9"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "3284b6d2ac454cf34f114e1d3319866fdd1e19cdc329999057e44ffe936cfa77"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  uuid:
    dependency: "direct main"
    description:
      name: uuid
      sha256: cd210a09f7c18cbe5a02511718e0334de6559871052c90a90c0cca46a4aa81c8
      url: "https://pub.dev"
    source: hosted
    version: "4.3.3"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "44cc7104ff32563122a929e4620cf3efd584194eec6d1d913eb5ba593dbcf6de"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.18"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "99fd9fbd34d9f9a32efd7b6a6aae14125d8237b10403b422a6a6dfeac2806146"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.13"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "1b4b9e706a10294258727674a340ae0d6e64a7231980f9f9a3d12e4b42407aad"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.16"
  vector_math:
    dependency: "direct main"
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "48941c8b05732f9582116b1c01850b74dbee1d8520cd7e34ad4609d6df666845"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.3"
  video_player_android:
    dependency: "direct overridden"
    description:
      name: video_player_android
      sha256: ae7d4f1b41e3ac6d24dd9b9d5d6831b52d74a61bdd90a7a6262a33d8bb97c29a
      url: "https://pub.dev"
    source: hosted
    version: "2.8.2"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: "84b4752745eeccb6e75865c9aab39b3d28eb27ba5726d352d45db8297fbd75bc"
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: df534476c341ab2c6a835078066fc681b8265048addd853a1e3c78740316a844
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "3ef40ea6d72434edbfdba4624b90fd3a80a0740d260667d91e7ecd2d79e13476"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.4"
  visibility_detector:
    dependency: "direct main"
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: ddfa8d30d89985b96407efce8acbdd124701f96741f2d981ca860662f1c0dc02
      url: "https://pub.dev"
    source: hosted
    version: "15.0.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "69da27e49efa56a15f8afe8f4438c4ec02eff0a117df1b22ea4aad194fe1c104"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  web:
    dependency: "direct overridden"
    description:
      name: web
      sha256: "97da13628db363c635202ad97068d47c5b8aa555808e7a9411963c533b449b27"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: "58c6666b342a38816b2e7e50ed0f1e261959630becd4c879c4f26bfa14aa5a42"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.5"
  webview_flutter:
    dependency: "direct main"
    description:
      name: webview_flutter
      sha256: "889a0a678e7c793c308c68739996227c9661590605e70b1f6cf6b9a6634f7aec"
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  webview_flutter_android:
    dependency: "direct overridden"
    description:
      name: webview_flutter_android
      sha256: "631093a7fbd93e9690ac61d8c8f3e857efbc189fc33f712b9ad6c01a623517ef"
      url: "https://pub.dev"
    source: hosted
    version: "4.3.3"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_web:
    dependency: "direct main"
    description:
      name: webview_flutter_web
      sha256: "18a7ccc1c31dd9a5c759a1b7217a2a1e04bd8f65712714a4070bfac19a23ca9e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3+4"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: c49a98510080378b1525132f407a92c3dcd3b7145bef04fb8137724aadcf1cf0
      url: "https://pub.dev"
    source: hosted
    version: "3.18.4"
  wechat_assets_picker:
    dependency: "direct main"
    description:
      name: wechat_assets_picker
      sha256: "65104fff598394fcf1c9a75a8a65a7aa9687485534b44d6e85275774d015df45"
      url: "https://pub.dev"
    source: hosted
    version: "9.5.0"
  wechat_picker_library:
    dependency: transitive
    description:
      name: wechat_picker_library
      sha256: a42e09cb85b15fc9410f6a69671371cc60aa99c4a1f7967f6593a7f665f6f47a
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  win32:
    dependency: "direct overridden"
    description:
      name: win32
      sha256: dc6ecaa00a7c708e5b4d10ee7bec8c270e9276dfcab1783f57e9962d7884305f
      url: "https://pub.dev"
    source: hosted
    version: "5.12.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: b9da305ac7c39faa3f030eccd175340f968459dae4af175130b3fc47e40d76ce
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  youtube_player_iframe:
    dependency: "direct main"
    description:
      path: "packages/youtube_player_iframe"
      ref: c2268281ca7f1f051f7ccab373e982d22c540910
      resolved-ref: c2268281ca7f1f051f7ccab373e982d22c540910
      url: "https://github.com/inspireui/youtube_player_flutter.git"
    source: git
    version: "4.0.3"
  youtube_player_iframe_web:
    dependency: transitive
    description:
      name: youtube_player_iframe_web
      sha256: c7020816031600349b56d2729d4e8be011fcb723ff7dc2dd0cdf72096a0e5ff4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
sdks:
  dart: ">=3.7.0 <4.0.0"
  flutter: ">=3.27.0"
